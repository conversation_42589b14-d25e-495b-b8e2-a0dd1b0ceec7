import cv2
import serial
import binascii
import time
import os
import re
import traceback
import shutil
import numpy as np
from pyorbbecsdk import *
from crc import crc16Add
from decimalM import transferToHex
from position_5th_dev_orbbec import process_vision_v5, PerformanceLogger, measure_time
from config import cfg
# add for yolov8-seg dev
from ultralytics import YOLO
# add for java server communication
import requests
from datetime import datetime
import subprocess
import torch
import pickle
# import diffusers

# ==================== Orbbec Gemini 335 Integration ====================
# Replaced Intel RealSense D435 with Orbbec Gemini 335 cameras
# Maintaining dual-camera functionality for intelligent equipment deployment
# ========================================================================

'''
2024/5/20 vision algorithum replaced with yolov8.1-seg
2024/8/20 add  ready signal and query power -grl
2025/03/24 add rtc
2025/04/08 4th code updated
2025/04/25 diameter only! V4.1
【250803】重写整个视觉代码，形成第5个版本
【250804-07】5th代码远程实机部署同步调试；大部分功能测试通过，但是1.5th_pre的聚类算法有bug；2.法线模型尚未部署测试
[250809] @TongXiang, 004-machine deploymeny debug for position_5th_dev
[250812] @TongXiang, 004-machine, add vision strategy selection by screen
【250814】@TongXiang，004-machine，add depth saving function
【250815】在machine007上安装pyorbbecsdk成功，之后在本地开发使用orbbec的主程序
【250816】将在本地开发的主程序在machine-007上部署测试
'''

# 全局参数

CFG = cfg()
TSIZE = CFG.TSIZE

# ==================== 第5版策略参数配置 ====================
# 策略参数格式: [pick_strategy, size_strategy, order_strategy, clustering, normal_calc]
# pick_strategy: 1=几何中心, 2=最高点, 3=最缓点
# size_strategy: 1=最长线段, 2=星型线段, 3=圆形拟合
# order_strategy: 1=按深度, 2=凸包算法, 3=圆度排序
# clustering: 1=DBSCAN聚类, 2=关闭聚类; [2508131500]auto: 1=DBSCAN, 2=Kmeans, 3=Close
# normal_calc: 1=计算法线, 2=不计算法线

# # 默认策略组合：几何中心 + 最长线段 + 按深度排序 + 不聚类 + 不计算法线
# DEFAULT_STRATEGY_PARAMS = [1, 1, 1, 2, 2]

# # 可以通过配置文件或环境变量来修改策略参数
# try:
#     with open('/home/<USER>/Desktop/queryBattery/strategy_params.txt', 'r') as f:
#         strategy_line = f.read().strip()
#         if strategy_line:
#             STRATEGY_PARAMS = [int(x) for x in strategy_line.split(',')]
#             print(f'从配置文件加载策略参数: {STRATEGY_PARAMS}')
#         else:
#             STRATEGY_PARAMS = DEFAULT_STRATEGY_PARAMS
# except:
#     STRATEGY_PARAMS = DEFAULT_STRATEGY_PARAMS
#     print(f'使用默认策略参数: {STRATEGY_PARAMS}')

STRATEGY_MAPS = {
    1: [1,1,3,3,2],  # baseline
    2: [2,1,3,3,2],  # highest point
    3: [1,1,1,3,2],  # high to low
    4: [1,1,2,3,2],  # out to inner
    5: [3,1,3,3,1],  # safe point
    6: [1,1,3,1,2],  # DBSCAN
    7: [1,1,3,2,2],  # K-means
    8: [1,1,3,2,2],  # normal output
}
with open('/home/<USER>/Desktop/queryBattery/diameter.txt','r') as file:
    line = file.readline().strip()
    if not line:
        print('xxxxxxxxxxxxxxxxxxx diameter.txt is empty!')
    else:
        try:
            number = int(line)
            if 1000 <= number <= 9999:
                first2 = number // 100
                mushroomD = first2
                last2 = number % 100
                STRATEGY_PARAMS = STRATEGY_MAPS.get(last2, 'vision strategy undefined!')
                print(f"==========Vision Strategy {last2} Set As: {STRATEGY_PARAMS}!================")
            else:
                print('xxxxxxxxxxxxxxxxxxx value not allowed and diameter set as 40!!')
                STRATEGY_PARAMS = [1,1,3,2,2]
                mushroomD = 40
        except ValueError:
            print('Vision strategy selection failed and use default strategy [1,1,3,3,2] with diameter 40!')
            STRATEGY_PARAMS = [1,1,3,2,2]
            mushroomD = 40
if mushroomD>0 :
    TSIZE = mushroomD/1000
    print('******mushroom Diameter set as:',TSIZE)
else:
    TSIZE = CFG.TSIZE
    print('******mushroom Diameter set as default value')
RSLUX = CFG.RSLUX
RSLUY = CFG.RSLUY
THRESHOLD = CFG.THRESHOLD
DEPTH_EXPOSURE = CFG.DEPTH_EXPOSURE
COLOR_EXPOSURE = CFG.COLOR_EXPOSURE
MODEL = CFG.MODEL
IP = CFG.IPURL
NEW_TERMINAL = CFG.NEW_TERMINAL
YOLOPATH = CFG.YOLOPATH
NORMALPATH = CFG.NORMALPATH
SIZEBIAS = CFG.SIZEBIAS

# 初始化性能记录器
performance_logger = PerformanceLogger(log_dir='/home/<USER>/yolov8-1/runs/logs/')

# 通讯协议
shootLBefore  =  'a311'  # shoot command before picking, return according to serial_n
shootRBefore  =  'a301'  # shoot command before picking, return according to serial_n
shootLAfter   =  'a312'  # shoot command after picking, return according to serial_n
shootRAfter   =  'a302'  # shoot command after picking, return according to serial_n

pickTime     =  'a303'  # time consumption per picking motion, return according to serial_n
mainReady    =  'a305'  # ready 
queryPower   =  'a3fe'  # query power  
setDiameter  =  'a306'  # set mushroom diameter 
setSysClock  =  'a308'  # set RTC

crcWrong     =  'a5ffffc5'  # re-send imtly
shootWrong   =  'a5eeeec5'  # re-start tx2
othercmd     =  'a50000c5'  # re-send imtly
readyRespond =  'a5ffffff00c5'  # re-send tx2 ready
dataSoc      =  'a5feffff12eaff0b085e1130000531610000000000002331030d020b4a0b3f' #默认电池信息

# # 连接显示外屏功能
# # 请求地址
# harvestCount_url = 'http://{}/transmitData'.format(IP)
# # 请求地址
# harvestDuration_url = 'http://{}/transmitData/time'.format(IP)
# start_url = 'http://{}/transmitData/start'.format(IP)

# temp dir
tempDir = '/home/<USER>/yolov8-1/runs/temp/'

def load_models(yolo_path, marigold_path):
    """加载YOLO和Marigold模型"""
    # 加载YOLO模型
    yolo_model = YOLO(yolo_path)
    print('yolo model done')
    
    # # 加载Marigold模型
    # pipe_normal = diffusers.MarigoldNormalsPipeline.from_pretrained(
    #     marigold_path, variant="fp16", torch_dtype=torch.float16, local_files_only=True
    # ).to("cuda")
    # print('diffusion model done')
    
    # return yolo_model, pipe_normal
    
    return yolo_model

# 相机采集图片后预热模型 for yolov8-seg - Orbbec Gemini 335 version
def Warmup(pipeline, yolo_model):
    try:
        while True:
            frames = pipeline.wait_for_frames(100)  # Orbbec uses timeout parameter
            if frames is None:
                print('*****Frame capture timeout, try again.*****')
                continue
            color_frame = frames.get_color_frame()  # 无需对齐
            depth_frame = frames.get_depth_frame()  # 只需验证相机与模型
            if not color_frame or not depth_frame:
                print('*****Frame capture error, try again.*****')
                continue
            else:
                print('-----Capture success-----')
                break
    except Exception as e:
        print(f'*****Orbbec Gemini 335 failed when warm up: {e}*****')
        return

    # Convert Orbbec color frame to BGR image
    from pyorbbecsdk_examples.utils import frame_to_bgr_image
    color_image = frame_to_bgr_image(color_frame)
    if color_image is None:
        print('*****Failed to convert Orbbec frame to image*****')
        return

    cv2.imwrite('Warmup.jpg', color_image)
    print('-----Save to Warmup.jpg-----')
    for i in range(2):
        start = time.time()
        yolo_model(color_image)
        print('-----warm up time: ', time.time()-start)
    print('-----Warm up done!-----')

# 接收串口的信号-g
def recv(serial):
    # time.sleep(0.5)
    while True:
        data = serial.read_all()  # 监听1
        if data == b'': # 如果无信号
            time.sleep(0.5)  # 假如串口没有接收到信号，就0.5s之后再监听一次
            continue
        else:#如果有信号
            print('=====Serial recive')
            print(time.strftime('=====Recive Time:          %Y-%m-%d %H:%M:%S', time.localtime()))
            break
    return data        

# 接收两个串口的二进制信号
def recv11(serial1, serial2, n):
    while True:
        if n == 1:  # 如果上次是1
            data = serial2.read_all()  # 则先监听2
            if data == b'':  # 如果2无信号
                data = serial1.read_all()  #  则再监听1
                
                if data == b'':  # 如果1也无信号
                    # print('-----2 Waiting for signal')
                    n = 2
                    time.sleep(0.5)  # 假如串口没有接收到信号，就0.5s之后再监听一次
                    continue
                else:  # 如果1有信号
                    print('=====Serial 1 recive')
                    print(time.strftime('=====Recive Time:          %Y-%m-%d %H:%M:%S', time.localtime()))
                    break
            else:  # 如果2有信号
                n = 2
                print('=====Serial 2 recive')
                print(time.strftime('=====Recive Time:          %Y-%m-%d %H:%M:%S', time.localtime()))
                break  # 假如接收到了信号，则停止监听，返回信号
        else:  # 如果上次是2
            data = serial1.read_all()  # 则先监听1    
            if data == b'':  # 如果1无信号
                data = serial2.read_all()  # 则监听2
                if data == b'':  # 如果2也无信号
                    # print('-----1 Waiting for signal')
                    n = 1
                    time.sleep(0.5)  # 假如串口没有接收到信号，就1s之后再监听一次
                    continue
                else:  #如果2有信号
                    print('=====Serial 2 recive')
                    print(time.strftime('=====Recive Time:          %Y-%m-%d %H:%M:%S', time.localtime()))
                    break
            else:  # 如果1有信号
                n = 1
                print('=====Serial 1 recive')
                print(time.strftime('=====Recive Time:          %Y-%m-%d %H:%M:%S', time.localtime()))
                break  # 假如接收到了信号，则停止监听，返回信号
    return n, data

# 整合命令判断函数
def judgeCommand(command):
    if len(command) < 12:
        return 'nullCmd'
    else:
        command = command[:22]  # max length = 12
        # print('command: ', command)
        signal = command[:4]  # 接受信息的关键信号
        print('=====signal: ', signal)
        crcContent = command[2:-6]  # 需要校验的内容
        checkCode = command[-6:-2]  # 接受到的校验码
        crcRes = crc16Add(crcContent)  # 主动计算的校验码
        if crcRes == checkCode:  # 如果两者一致，说明信号传输未出错
            # 判断信号类别
            # if signal == setSize:
            #     return 'setSize'
            if signal == shootLBefore:
                return 'shootLBefore'
            elif signal == shootRBefore:
                return 'shootRBefore'
            elif signal == shootRAfter:
                return 'shootRAfter'    
            elif signal == shootLAfter:
                return 'shootLAfter'
            elif signal == pickTime:
                
                # if pickTime, send immediately 2024/05/21
                send_timedata = int(command[4:6])  # s
                print('\n#####picktime : ', send_timedata)
                sendTime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                data = {'harvestDuration': send_timedata, 'flag': 1, 'sendTime': sendTime}  # not yet serial_n!
                # response = requests.post(harvestDuration_url, data=data)
                # print('#####pick time reponse: ', response.text)
                
                return 'pickTime'
            elif signal == mainReady:
                return 'mainReady'
            elif signal == queryPower:
                return 'queryPower'
            elif signal == setDiameter:
                diameterMushroom = int(command[4:6],16)
                diameterMushroomc = int(command[6:8],16)
                with open('/home/<USER>/Desktop/queryBattery/diameter.txt','w') as file:
                    file.write(str(diameterMushroom*100 + diameterMushroomc))
                print('====mushroom diameter as:',diameterMushroom) 
                
                return 'setDiameter'
            elif signal == setSysClock:
                return 'setRTC'
            else:
                return 'nullCmd'
        else:
            return 'crcWrong'

def save_depth_data(depth_frame, depth_intrin, filename):
    """
    保存Orbbec Gemini 335深度帧的最原始数据和必要参数。

    :param depth_frame: 由 pipeline.wait_for_frames() 获取的深度帧 (Orbbec depth frame)
    :param depth_intrin: 深度相机内参 (Orbbec intrinsics)
    :param filename: 保存的HDF5文件名 (str)
    """
    try:
        # Import h5py only when needed
        import h5py

        # 1. 获取原始深度数据 (uint16, mm)
        depth_data_z16 = np.frombuffer(depth_frame.get_data(), dtype=np.uint16).reshape(
            (depth_frame.get_height(), depth_frame.get_width()))

        # 2. 保存到HDF5文件
        with h5py.File(filename, 'w') as f:
            # 保存原始深度数据
            f.create_dataset('depth_data_z16', data=depth_data_z16, dtype='uint16', compression='gzip')

            # 保存深度单位
            depth_scale = depth_frame.get_depth_scale()
            f.attrs['depth_scale'] = depth_scale

            # 保存相机内参 - Orbbec format
            f.attrs['width'] = depth_intrin.width
            f.attrs['height'] = depth_intrin.height
            f.attrs['cx'] = depth_intrin.cx  # Orbbec uses cx/cy instead of ppx/ppy
            f.attrs['cy'] = depth_intrin.cy
            f.attrs['fx'] = depth_intrin.fx
            f.attrs['fy'] = depth_intrin.fy
            # Note: Orbbec intrinsics structure may differ from RealSense

        # print(f"Minimal depth data saved to {filename}")
        # print(f"  - Data shape: {depth_data_z16.shape}")
        # print(f"  - Data type: {depth_data_z16.dtype}")
        # print(f"  - Depth scale: {depth_scale}")
        # print(f"  - Intrinsics: {depth_intrin.width}x{depth_intrin.height}, "
        #       f"fx={depth_intrin.fx:.2f}, fy={depth_intrin.fy:.2f}, "
        #       f"cx={depth_intrin.cx:.2f}, cy={depth_intrin.cy:.2f}")

    except Exception as e:
        print(f"Error saving depth data: {e}")

def save_depth_data_pickle(depth_frame, depth_intrin, filename):
    """
    使用pickle保存Orbbec Gemini 335深度帧的最原始数据和必要参数。
    """
    try:
        # Get depth data from Orbbec frame
        depth_data_z16 = np.frombuffer(depth_frame.get_data(), dtype=np.uint16).reshape(
            (depth_frame.get_height(), depth_frame.get_width()))
        depth_scale = depth_frame.get_depth_scale()

        # Orbbec intrinsics对象转换为字典
        intrin_dict = {
            'width': depth_intrin.width,
            'height': depth_intrin.height,
            'cx': depth_intrin.cx,  # Orbbec uses cx/cy instead of ppx/ppy
            'cy': depth_intrin.cy,
            'fx': depth_intrin.fx,
            'fy': depth_intrin.fy,
            # Note: Orbbec may have different distortion model
        }

        data_to_save = {
            'depth_data_z16': depth_data_z16,
            'depth_scale': depth_scale,
            'depth_intrin': intrin_dict
        }

        with open(filename, 'wb') as f:
            pickle.dump(data_to_save, f, protocol=pickle.HIGHEST_PROTOCOL)

        # print(f"[SAVE] Data saved to '{filename}'")
        # print(f"       - Shape: {depth_data_z16.shape}, Type: {depth_data_z16.dtype}")
        # print(f"       - Depth Scale: {depth_scale}")
        # file_size_mb = os.path.getsize(filename) / (1024 * 1024)
        # print(f"       - File Size: {file_size_mb:.2f} MB")
    except Exception as e:
        print(f"xxxxxxxxxxxxxxxxxxError saving depth data: {e}")

# 采集图像、模型推理并返回信息 - Orbbec Gemini 335 version
# def MainProcess(serial_n, pipeline, yolo_model, normal_pipe, shootFailCmd, commandFlag):
def MainProcess(serial_n, pipeline, yolo_model, shootFailCmd, commandFlag):
    print('=====Receive shoot command and Capture start')
    shoot_flag = 1
    try:  # 尝试采集图像
        while True:
            frames = pipeline.wait_for_frames(100)  # Orbbec uses timeout parameter
            if frames is None:
                continue
            color_frame = frames.get_color_frame()
            depth_frame = frames.get_depth_frame()
            if not depth_frame or not color_frame:
                continue
            print('=====Capture success!')
            break  # 如果获取深度图和彩色图都成功，则跳出while循环

    except Exception as e:  # 如果采集图像失败，则发送拍照失败信息。此处只尝试一次
        traceback.print_exc()
        shoot_flag = 0
        print(f'*****Orbbec Gemini 335 failed when main run: {e}*****')
        # serial.write(bytes.fromhex(shootFailCmd))
        # print('=====Send shoot_fail: ', shootFailCmd)  # 回传拍照失败信号
        return shootFailCmd, shoot_flag

    # Get intrinsics from Orbbec depth frame
    depth_intrin = depth_frame.get_stream_profile().as_video_stream_profile().get_intrinsic()

    # Convert Orbbec color frame to BGR image
    from pyorbbecsdk_examples.utils import frame_to_bgr_image
    color_image = frame_to_bgr_image(color_frame)
    if color_image is None:
        print('*****Failed to convert Orbbec color frame to image*****')
        return shootFailCmd, 0

    # Process depth frame for visualization (Orbbec doesn't use the same filter pipeline)
    # Create depth visualization directly
    width = depth_frame.get_width()
    height = depth_frame.get_height()
    depth_data = np.frombuffer(depth_frame.get_data(), dtype=np.uint16).reshape((height, width))
    depth_data_float = depth_data.astype(np.float32) * depth_frame.get_depth_scale()

    # Apply depth range filtering
    MIN_DEPTH = 10  # 10mm
    MAX_DEPTH = 500  # 500mm
    depth_data_filtered = np.where((depth_data_float > MIN_DEPTH) & (depth_data_float < MAX_DEPTH),
                                   depth_data_float, 0).astype(np.uint16)

    # Create colorized depth image
    depth_image = cv2.normalize(depth_data_filtered, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
    depth_image = cv2.applyColorMap(depth_image, cv2.COLORMAP_JET)
    
    imageName = time.strftime('{}_%Y%m%d%H%M%S_color_{}.jpg'.format(serial_n, commandFlag[5:]), time.localtime())
    color_imagePath = tempDir + imageName
    cv2.imwrite(color_imagePath, color_image)

    depth_imagePath = tempDir + imageName[:-10] + '_depth.jpg'
    cv2.imwrite(depth_imagePath, depth_image)

    depth_dataPath = tempDir + imageName[:-10] + '_depth.pkl'  # 设置保存深度数据的文件名
    save_depth_data_pickle(depth_frame, depth_intrin, depth_dataPath)

    # 模型推理
    
    # # fake image for debug
    # print('==========FAKE NEWS==========')
    # fake_colorimg_path = '/home/<USER>/DataAll/val/2_20210615123522_color.jpg'
    # color_imagePath = fake_colorimg_path
    # color_image = cv2.imread(color_imagePath)
    
    # 开始性能记录会话
    performance_logger.start_session(imageName)

    # model inference
    yolo_start = time.time()
    results = yolo_model(color_image, conf=THRESHOLD, max_det=100)
    yolo_time = time.time() - yolo_start
    # print('-----results.mask: ', results[0].masks.data.size())
    print('-----YOLO model inference done-----')
    print(f'-----YOLO inference time: {yolo_time:.3f}s-----')

    # # 原始版本的法线功能实现：Marigold模型法线估计
    # start = time.time()
    # marigold_image = diffusers.utils.load_image(color_imagePath)
    # normals = normal_pipe(marigold_image).prediction
    # print('-----Speed normal: ', time.time()-start)
    # # 法线图片可视化
    # vis_normal = normal_pipe.image_processor.visualize_normals(normals)
    # normal_imagePath = tempDir + imageName[:-10] + '_normal.jpg'
    # vis_normal[0].save(normal_imagePath)
    # print('-----Diffusion normals done-----')

    # 记录YOLO推理时间
    performance_logger.record_yolo_time(yolo_time)

    # Marigold法线估计（当前版本中已禁用）
    marigold_time = 0.0  # 当前版本不使用Marigold
    # # 原始版本的法线功能实现：Marigold模型法线估计
    # start = time.time()
    # marigold_image = diffusers.utils.load_image(color_imagePath)
    # normals = normal_pipe(marigold_image).prediction
    # marigold_time = time.time() - start
    # print(f'-----Marigold inference time: {marigold_time:.3f}s-----')
    # # 法线图片可视化
    # vis_normal = normal_pipe.image_processor.visualize_normals(normals)
    # normal_imagePath = tempDir + imageName[:-10] + '_normal.jpg'
    # vis_normal[0].save(normal_imagePath)
    # print('-----Diffusion normals done-----')
    
    # 记录Marigold推理时间（即使是0）
    performance_logger.record_marigold_time(marigold_time)
    combine_imagePath = tempDir + imageName[:-10] + '_combine.jpg'

    # 使用第5版视觉系统处理
    result_image, targets_info, processing_info = process_vision_v5(
        imgrgb=color_image,
        savePath=combine_imagePath,
        results=results[0],
        depth_frame=depth_frame,
        depth_intrin=depth_intrin,
        TSIZE=TSIZE,
        SIZEBIAS=SIZEBIAS,
        strategy_params=STRATEGY_PARAMS,
        imgname=imageName,
        save_normal=(STRATEGY_PARAMS[4] == 1),  # 根据策略参数决定是否保存法线图
        logger=performance_logger
    )

    # 转换为原有格式以保持兼容性
    aft_dets = []
    for target in targets_info:
        # 格式: [safe_x, safe_y, detection[2], detection[3], center_point[0], center_point[1], center_point[2], size]
        world_point = target.get('world_point', [0, 0, 0])
        pick_info = target.get('pick_info', {})
        normal = pick_info.get('normal', [0, 0, 1]) if 'normal' in pick_info else [0, 0, 1]

        det = [
            world_point[0],  # safe_x (world coordinate)
            world_point[1],  # safe_y (world coordinate)
            world_point[2],  # safe_z (world coordinate)
            normal[0],       # normal_x
            normal[1],       # normal_y
            normal[2],       # normal_z
            target.get('size', 0)  # size
        ]
        aft_dets.append(det)

    # results visualization
    # visual(results[0], aft_dets, color_imagePath, serial_n)
    
    # 测试深度信息获取情况
    # visual_test(detections, aft_dets, color_imagePath, depth_frame, depth_intrin)

    message = transferToHex(len(aft_dets), 2)
    for det in aft_dets:
        # dets.append([safe_x, safe_y, detection[2], detection[3], center_point[0], center_point[1], center_point[2], size])
        message += transferToHex(int(det[0]*10000), 4) + transferToHex(int(det[1]*10000), 4) + transferToHex(int(det[2]*10000), 4) + transferToHex(int(det[3]), 4) + transferToHex(int(det[4]), 4) + transferToHex(int(det[5]), 4)
        print('-----Pick xyz: {:.1f},{:.1f},{:.1f}'.format(det[0]*10000, det[1]*10000, det[2]*10000))
        print('-----Pick normal: {:.3f},{:.3f},{:.3f}\n'.format(det[3], det[4], det[5]))
    crcCode = crc16Add(message)
    message = 'a5' + message + crcCode + 'c5'
    return message, shoot_flag

# 检测结果可视化 for segmetation mask and info draw
def visual(results, aft_dets, colorimg_path, serial_n):
    detshowimg_path = tempDir + re.split('[./]', colorimg_path)[-2] + '_det.jpg'
    results.save(filename=detshowimg_path)
    
    # 可视化：将筛选排序后的顺序输出
    infshow_img = cv2.imread(detshowimg_path)
    
    # area where can be picked
    if serial_n == 1:
        cv2.rectangle(infshow_img, (240, 30), (610, 450), (255, 255, 255), 2)
    else:
        cv2.rectangle(infshow_img, (315, 30), (675, 450), (255, 255, 255), 2)
    
    n = 0
    for det in aft_dets:
        n += 1
        
        cv2.circle(infshow_img, (int(det[0]), int(det[1])), 2, (0,255,0), 2)
        cv2.rectangle(infshow_img, (int(det[0]-det[2]*0.5), int(det[1]-det[3]*0.5)), (int(det[0]+det[2]*0.5), int(det[1]+det[3]*0.5)), (255, 255, 255), 2)
        
        label1 = f"{n}: {det[6]*1000:.1f}"
        pos1 = (int(det[0]-27), int(det[1]-2))
        cv2.putText(infshow_img, label1,
                    pos1,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 0, 255),
                    1)  # line type
        label2 = f"{det[7]*1000:.1f}"
        pos2 = (int(det[0]-27), int(det[1]+10))
        cv2.putText(infshow_img, label2,
                    pos2,
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.4,  # font scale
                    (0, 0, 255),
                    1)  # line type

    infshowimg_path = tempDir + re.split('[./]', colorimg_path)[-2] + '_inf.jpg'
    cv2.imwrite(infshowimg_path, infshow_img)

    # collect img data to be sent to server
    # send image
    send_img = cv2.imread(infshowimg_path)
    _, send_img_encoded = cv2.imencode('.jpg', send_img)
    send_imgdata = {'photo': (os.path.basename(infshowimg_path), send_img_encoded.tobytes(), 'image/jpeg', {'Expires': '0'})}
    # send data
    send_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    senf_infodata = {'counts': results.boxes.conf.size()[0] ,'harvestCount': len(aft_dets), 'flag': serial_n-1, 'photoTime': send_time}
    print('\n#####send info data counts, harvest, flag, time: ', results.boxes.conf.size()[0], len(aft_dets), serial_n-1, send_time)
    # 请求
    # response = requests.post(harvestCount_url, files=send_imgdata, data=senf_infodata)
    # print('#####request post and response: " ', response.text)



if __name__ == '__main__':
    
    
    # 开启单路串口信号
    serial1 = serial.Serial(port='/dev/ttyTHS0', baudrate=9600, parity=serial.PARITY_NONE,
                           stopbits=serial.STOPBITS_ONE, bytesize=serial.EIGHTBITS, timeout=1)
    #电池串口
    #serialSoc = serial.Serial(port='/dev/ttyUSB0', baudrate=9600, parity=serial.PARITY_NONE,
    #                       stopbits=serial.STOPBITS_ONE, bytesize=serial.EIGHTBITS, timeout=1)
    # 判断串口连接情况600
    if serial1.isOpen():
        print("-----Serial1 connect!-----")
        serial1.reset_input_buffer()
        serial1.reset_output_buffer()
    else :
        print("xxxxxSerial1 232 failed!xxxxx")
    '''if serialSoc.isOpen():
        print("-----Serial Soc 485 connect!-----")
        serialSoc.reset_input_buffer()
        serialSoc.reset_output_buffer()
    else :
        print("xxxxxSerial Soc 485 failed!xxxxx")
	'''

    # set the model
    # yolo_model, normal_pipe = load_models(YOLOPATH, NORMALPATH)
    yolo_model = load_models(YOLOPATH, NORMALPATH)
    print('-----Model init done!-----')

    # ==================== Orbbec Gemini 335 双相机初始化 ====================
    # 输出双相机信息
    ctx = Context()
    device_list = ctx.query_devices()
    device_count = device_list.get_count()

    if device_count == 0:
        print("*****No Orbbec devices connected*****")
        exit(1)
    elif device_count < 2:
        print(f"*****Only {device_count} Orbbec device(s) connected, need 2 for dual-camera setup*****")
        exit(1)

    print(f"-----Found {device_count} Orbbec Gemini 335 cameras-----")
    for i in range(device_count):
        device = device_list.get_device_by_index(i)
        device_info = device.get_device_info()
        serial_number = device_info.get_serial_number()
        print(f'-----Camera {i+1} serial number: {serial_number}-----')

    # 设置两个Orbbec Gemini 335相机
    # Note: Orbbec doesn't use the same post-processing pipeline as RealSense
    # We'll handle depth processing directly in the MainProcess function

    # Create align filter for depth-to-color alignment (if needed)
    # align_filter = AlignFilter(align_to_stream=OBStreamType.COLOR_STREAM)  # Currently not used in processing

    # start pipelines - Orbbec Gemini 335 setup
    # for camera 1
    device1 = device_list.get_device_by_index(0)
    pipeline1 = Pipeline(device1)
    config1 = Config()

    # Configure depth stream
    try:
        depth_profile_list = pipeline1.get_stream_profile_list(OBSensorType.DEPTH_SENSOR)
        # depth_profile = depth_profile_list.get_default_video_stream_profile()
        depth_profile = depth_profile_list.get_video_stream_profile(848, 480, OBFormat.Y16, 30)
        config1.enable_stream(depth_profile)
    except Exception as e:
        print(f"Failed to configure depth stream for camera 1: {e}")

    # Configure color stream
    try:
        color_profile_list = pipeline1.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
        # color_profile = color_profile_list.get_default_video_stream_profile()
        color_profile = color_profile_list.get_video_stream_profile(848, 480, OBFormat.RGB, 30)
        config1.enable_stream(color_profile)
    except Exception as e:
        print(f"Failed to configure color stream for camera 1: {e}")

    print('-----Orbbec Gemini 335 Camera 1 init done!-----')

    # for camera 2
    device2 = device_list.get_device_by_index(1)
    pipeline2 = Pipeline(device2)
    config2 = Config()

    # Configure depth stream
    try:
        depth_profile_list = pipeline2.get_stream_profile_list(OBSensorType.DEPTH_SENSOR)
        depth_profile = depth_profile_list.get_default_video_stream_profile()
        config2.enable_stream(depth_profile)
    except Exception as e:
        print(f"Failed to configure depth stream for camera 2: {e}")

    # Configure color stream
    try:
        color_profile_list = pipeline2.get_stream_profile_list(OBSensorType.COLOR_SENSOR)
        color_profile = color_profile_list.get_default_video_stream_profile()
        config2.enable_stream(color_profile)
    except Exception as e:
        print(f"Failed to configure color stream for camera 2: {e}")

    print('-----Orbbec Gemini 335 Camera 2 init done!-----')
    # 双相机开始工作 - Orbbec Gemini 335
    try:
        pipeline1.start(config1)
        print('-----Orbbec Gemini 335 Camera 1 pipeline started!-----')

        # Configure camera 1 settings if supported
        try:
            # Note: Orbbec camera property control may differ from RealSense
            # Property setting will be implemented based on specific Orbbec SDK documentation
            print(f'-----Camera 1 configured (exposure settings may need SDK-specific implementation)-----')
        except Exception as e:
            print(f'Warning: Could not configure camera 1: {e}')

    except Exception as e:
        print(f'*****Failed to start camera 1 pipeline: {e}*****')

    time.sleep(0.5)  # 停顿以确保相机稳定

    try:
        pipeline2.start(config2)
        print('-----Orbbec Gemini 335 Camera 2 pipeline started!-----')

        # Configure camera 2 settings if supported
        try:
            # Note: Orbbec camera property control may differ from RealSense
            # Property setting will be implemented based on specific Orbbec SDK documentation
            print(f'-----Camera 2 configured (exposure settings may need SDK-specific implementation)-----')
        except Exception as e:
            print(f'Warning: Could not configure camera 2: {e}')

    except Exception as e:
        print(f'*****Failed to start camera 2 pipeline: {e}*****')

    print('-----Orbbec Gemini 335 pipelines start!-----')
    # pipeline1, config1, pipeline2, config2 = startPipeline(devices)
    
    # 采集图片并模型warmup
    print('\n-----Pipeline1 warmup:')
    # Warmup(pipeline1, yolo_model, normal_pipe)
    Warmup(pipeline1, yolo_model)
    
    print('\n-----Pipeline2 warmup:')
    # Warmup(pipeline2, yolo_model, normal_pipe)
    Warmup(pipeline2, yolo_model)

    # # open new terminal and python code
    # subprocess.run(['gnome-terminal', '--', 'python', NEW_TERMINAL])
    # print('\n-----new terminal start')

    # 主程序
    # response = requests.post(start_url)
    count = 0
    serial_n = 1  # 设置其第一次去监听串口
    camNo = 1   #camera number
    SocFlg = False
    # dataSocSer =  str(dataSoc)
    # serial1.flushInput()
    # bug
    while True:
        try:
        	
            '''if SocFlg  :
                SocFlg = False
                print('===== usb query bettary')
                serialSoc.write(bytes.fromhex("DDA50300FFFD77"))
                timestampSocStart = datetime.now().timestamp()
                print("=====battery timestampSocStart :", timestampSocStart)
                while True:
                    dataSoc = serialSoc.read_all()
                    if (datetime.now().timestamp()-timestampSocStart)>1.0 :
                        print("=====battery capacity :", dataSoc)
                        #dataSocStr = str(dataSoc)
                        break
                    elif dataSoc == b'':
                        #print("=====battery capacity :", dataSoc)
                        #dataSocStr = str(dataSoc)
                        continue
                    else:
                        if len(dataSoc)>=32 :
                            dataSocSer =  'a5feffff'+ str(binascii.b2a_hex(dataSoc))[10:64] 
                        print("=====battery capacity :", dataSocSer)
                        break
                    
                    sleep(0.5)'''
            # ''' for debug, watch out the input image!
            print("\n-----Waiting for signal...\n")
            #serial_n, data = recv(serial1, serial2, serial_n)  # 从两路串口判断接收二进制信号，及其来源
            
            # listen to signal
            data = recv(serial1)
            count += 1
            print('=====Signal count since current running: ', count)
            print('=====Serial receive data: {}'.format(data)) # serial_n, data))
            command = str(binascii.b2a_hex(data))[2:-1]  # 转换为十六进制命令
            print('=====Convert to command:     ', command)
            #crcWrong = 'a5ff' + command[2:4] + command[6:8] + 'ffffc5'
            #shoot_fail = 'a5ee' + command[2:4] + command[6:8] + 'eeeec5'
            # 判断命令
            commandFlag = judgeCommand(command)
            # '''
            
            # # for debug 视觉部分独立测试：注释掉串口动作和指令判断
            # time.sleep(5)  # 手动操作延时
            # commandFlag = 'shootLBefore'
            
            print('=====commandFlag:', commandFlag)
            message = None

            # 根据接收的指令编号判断发送指令的串口
            # if serial_n == 1:
            serial = serial1
            
            print('-----Serial 1 and Orbbec Gemini 335 Camera 1: ')
            # else:
            #     serial = serial2
            #     pipeline = pipeline2
            #     print('-----Serial 2 and Realsense 2: ')
            if commandFlag[:5] == 'shoot': 
                if commandFlag[:6] == 'shootL':
                    camNo = 2
                    pipeline = pipeline2
                    print("=====left camera pic=========")
                elif commandFlag[:6] == 'shootR':
                    camNo = 1
                    pipeline = pipeline1
                    print("=====right camera pic=========") 
                else:
                    print("=====serial receive wrong =========")
            # if commandFlag == 'crcWrong':
                # serial.write(bytes.fromhex(crcWrong))
                # print("=====Send command crc wrong :", crcWrong)
            if commandFlag[:5] == 'shoot':
                print('-----Serial_{} recieve command {}-----\n'.format(camNo, commandFlag))
                # message, shoot_flag = MainProcess(serial_n, pipeline, yolo_model, normal_pipe, shootWrong, commandFlag)
                message, shoot_flag = MainProcess(serial_n, pipeline, yolo_model, shootWrong, commandFlag)
                if shoot_flag:
                    print("=====Send coordinate info to serial_{}: {}".format(camNo, message))
                else:  # if shoot failed, not try to restart pipeline any more
                    print('=====Send shoot_fail: ', message)  # 回传拍照失败信号
                
                
                serial.write(bytes.fromhex(message))
                print(time.strftime('=====Send time:            %Y-%m-%d %H:%M:%S', time.localtime()))
                
                # fake_command = 'a5010501a2fe13085501a4fe8108600234fe7b0885ff64feaa082c027fff85088444e3c5'
                # serial.write(bytes.fromhex(fake_command))
                # print("=====Send fake command: ", fake_command)
            
            elif commandFlag == 'pickTime':
                print('-----Serial_{} recieve command pickTime and info has been transmit-----\n'.format(serial_n))
                continue
            elif commandFlag == 'mainReady':
                serial.write(bytes.fromhex(readyRespond))
                print("=====this device and system are ready! :", readyRespond)
                continue
            elif commandFlag == 'queryPower':
                #SocFlg =  True
                #serial.write(bytes.fromhex(dataSocSer))
                #serial.write(dataSoc)
                print("=====battery capacity :")#, dataSocSer)
                # with open('/home/<USER>/Desktop/queryBattery/battery.txt','r') as file:
                #     content = file.read()
                #     #sendmsg = content
                #     #sendmsg= bytes.fromhex(content)
                #     #crcCodePwr = crc16Add(sendmsg)
                #     #sendmsg =  + sendmsg +  'c5'
                #     serial.write(bytes.fromhex(content))
                #     print("=====battery capacity :", content)
                continue
            elif commandFlag == 'setDiameter' :
                print("=====set diameter ")
            elif commandFlag == 'setRTC' :
                iRTC_Y = int(command[4:6],16)+2000
                iRTC_m = int(command[6:8],16)
                iRTC_d = int(command[8:10],16)
                iRTC_H = int(command[10:12],16)
                iRTC_M = int(command[12:14],16)
                iRTC_S = int(command[14:16],16)
                time_str=f"{iRTC_Y:04d}-{iRTC_m:02d}-{iRTC_d:02d} {iRTC_H:02d}:{iRTC_M:02d}:{iRTC_S:02d}"
                print('====formate time is:',time_str) 
                npawd= "nvidia"
                textTerminal = f"echo{npawd}| sudo -S date -s '{time_str}'"
                subprocess.call(textTerminal,shell=True)
                print("====rtc set scuessful") 
                
            else:
                print('=====Command NULL.')
                #serial.write(bytes.fromhex(othercmd))
                print("=====Send command unknown: ", othercmd)
                print(time.strftime('=====Send time:            %Y-%m-%d %H:%M:%S', time.localtime()))
           
            # time.sleep(1.0)
        except: #Exception as ex:
            print('-----Main code error or keyboard corrupt: ')
            traceback.print_exc()
            try:
                pipeline1.stop()
                pipeline2.stop()
                print('-----Orbbec Gemini 335 pipelines stopped-----')
            except Exception as e:
                print(f'Error stopping pipelines: {e}')

            # serial1.reset_input_buffer()
            # serial1.reset_output_buffer()
            serial1.flush()
            serial1.close()

            # serial2.reset_input_buffer()
            # serial2.reset_output_buffer()
            # serial2.flush()
            # serial2.close()
            break
        if len(os.listdir(tempDir)) > 800000:
            print('-----Clear temp and rebuild it')
            shutil.rmtree(tempDir)
            os.mkdir(tempDir)
            count = 0
    print('-----Main code Exit-----')
    serial1.close()
    # Clean up Orbbec resources
    try:
        pipeline1.stop()
        pipeline2.stop()
        print('-----Orbbec Gemini 335 resources cleaned up-----')
    except:
        pass
  #  serial2.close()
