#!/usr/bin/env python3
"""
Quick check for PositionClustering in position_5th_dev.py without camera.
- Tries to read target_info from CSV (vision_performance_20250806_155311.csv or vision_performance.csv)
- If not found/parsing fails, uses simulated data
- Runs clustering and prints summary
"""
import os
import json
import csv
import math
from typing import List, Dict, Any

from position_5th_dev import PositionClustering

# Optional import of helper functions from validation script
try:
    from test_clustering_validation import create_mock_data  # type: ignore
except Exception:
    create_mock_data = None


def read_csv_target_info(csv_path: str):
    if not os.path.exists(csv_path):
        return None
    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            first = next(reader)
            if 'target_info' not in first:
                return None
            raw = first['target_info']
            try:
                return json.loads(raw)
            except json.JSONDecodeError:
                # try eval fallback only for local test convenience
                try:
                    cleaned = raw.strip().strip('"').strip("'")
                    return eval(cleaned)
                except Exception:
                    return None
    except Exception:
        return None


def normalize_targets(target_info: List[Any]) -> List[Dict[str, float]]:
    """Convert to list of dicts with pixel_x/pixel_y."""
    out = []
    for i, t in enumerate(target_info):
        if isinstance(t, dict) and ('pixel_x' in t and 'pixel_y' in t):
            try:
                out.append({'pixel_x': float(t['pixel_x']), 'pixel_y': float(t['pixel_y'])})
            except Exception:
                continue
        elif isinstance(t, (list, tuple)) and len(t) >= 3:
            try:
                out.append({'pixel_x': float(t[1]), 'pixel_y': float(t[2])})
            except Exception:
                continue
    return out


def main():
    candidates = [
        'vision_performance_20250806_155311.csv',
        'vision_performance.csv',
    ]
    target_info = None
    for path in candidates:
        target_info = read_csv_target_info(path)
        if target_info:
            print(f"Loaded target_info from {path} (n={len(target_info)})")
            break

    if target_info is None:
        if create_mock_data:
            print("No CSV available, using simulated data from create_mock_data()")
            target_info = create_mock_data()
        else:
            print("No CSV and no simulator available, generating simple synthetic points")
            # 3 clusters
            import random
            centers = [(160, 120), (480, 120), (320, 360)]
            pts = []
            for cx, cy in centers:
                for _ in range(15):
                    pts.append({'pixel_x': cx + random.gauss(0, 30), 'pixel_y': cy + random.gauss(0, 30)})
            target_info = pts

    targets_info = normalize_targets(target_info)
    print(f"Using {len(targets_info)} points for clustering")

    # Use eps similar to validation (70) unless you want to try the class default
    clustering = PositionClustering(n_clusters=None, max_distance=70)
    result = clustering.cluster_targets(targets_info)

    labels = result.get('cluster_labels', [])
    centers = result.get('cluster_centers', [])
    sizes = result.get('cluster_sizes', [])

    n_clusters = len([l for l in set(labels) if isinstance(l, int) and l >= 0])
    print("\n=== Clustering summary ===")
    print(f"clusters: {n_clusters}")
    for cid in range(n_clusters):
        print(f"  C{cid}: size={sizes[cid] if cid < len(sizes) else '?'} center={centers[cid] if cid < len(centers) else '?'}")


if __name__ == '__main__':
    main()

