#from crc import crc16Add

# transfer decimal to hex
def transferToHex(input, num):
    #print('\ninput: ', input)
    #judge if positive and negtive value
    if input >= 0:
        hexa = hex(input)
    else:
        hexa = hex(((abs(input) ^ 0xffff) + 1) & 0xffff)        # 补码的计算方式      '^' means xor
    #print('hexa: ', hexa)
    temp = list(hexa)
    temp = temp[2:]
    length = len(temp)
    if num == 2:
        if length < 2:
            for i in range(2-length):
                temp.insert(0, '0')
    if num == 4:
        if length < 4:
            for i in range(4-length):
                temp.insert(0, '0')
    #res = "".join(temp[0:2]) + ' ' + "".join(temp[2:])
    res = ''.join(temp)
    #print('res: ', res)
    return res

def transferToN(input):
    # input range 220*440 mm
    #judge if positive and negtive value
    if input >= 0:
        hexa = hex(input)
    else:
        hexa = hex(((abs(input) ^ 0xffff) + 1) & 0xffff)        # 补码的计算方式      '^' means xor
    #print(hexa)
    temp = list(hexa)
    temp = temp[2:]
    if len(temp) == 1:              #padding 3 number
        temp.insert(0, '0')
    else:
        print("doesn't need to padding")

    res = "".join(temp[0:2])
    return res


if __name__ == '__main__':
    test = transferToHex(2200, 4)
    print('res: ', test, type(test))