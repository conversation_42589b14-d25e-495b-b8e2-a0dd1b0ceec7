
<!DOCTYPE html>
<html>
<head>
    <title>K-means 聚类结果可视化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .canvas {
            border: 2px solid #333;
            position: relative;
            width: 848px;
            height: 480px;
            background-color: #f0f0f0;
            margin: 20px 0;
        }
        .point {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: 1px solid #000;
        }
        .noise {
            background-color: #888;
            width: 6px;
            height: 6px;
            transform: rotate(45deg);
            border-radius: 0;
        }
        .legend {
            margin: 10px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid #000;
        }
    </style>
</head>
<body>
    <h1>K-means 聚类结果可视化</h1>
    <p>图像尺寸: 848x480 像素</p>
    <p>总目标数量: 20</p>

    <div class="legend">
<div class="legend-item"><div class="legend-color" style="background-color: #FF0000;"></div><span>簇 0 (2个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #00FF00;"></div><span>簇 1 (3个目标)</span></div><div class="legend-item"><div class="legend-color" style="background-color: #0000FF;"></div><span>簇 2 (15个目标)</span></div>
    </div>

    <div class="canvas">
<div class="point" style="left: 60.55984824560838px; top: 65.44108488380768px; background-color: #00FF00;"></div><div class="point" style="left: 63.02251536007381px; top: 55.60525603498367px; background-color: #00FF00;"></div><div class="point" style="left: 71.15848796949759px; top: 64.49680211004735px; background-color: #00FF00;"></div><div class="point" style="left: 103.9131541385988px; top: 23.64238565727718px; background-color: #FF0000;"></div><div class="point" style="left: 140.70097628741888px; top: 56.13344738319525px; background-color: #FF0000;"></div><div class="point" style="left: 459.2926229961278px; top: 91.92045603335099px; background-color: #0000FF;"></div><div class="point" style="left: 503.67831299043667px; top: 167.67147150719248px; background-color: #0000FF;"></div><div class="point" style="left: 483.20908904982537px; top: 104.80078317900517px; background-color: #0000FF;"></div><div class="point" style="left: 439.68612283244494px; top: 119.72166097753968px; background-color: #0000FF;"></div><div class="point" style="left: 481.3441074660462px; top: 147.31962175360832px; background-color: #0000FF;"></div><div class="point" style="left: 508.96886880828026px; top: 135.64291417179624px; background-color: #0000FF;"></div><div class="point" style="left: 474.4545775646591px; top: 130.9252562243764px; background-color: #0000FF;"></div><div class="point" style="left: 499.596228898105px; top: 136.74501838552675px; background-color: #0000FF;"></div><div class="point" style="left: 463.3971259500124px; top: 117.31527362810466px; background-color: #0000FF;"></div><div class="point" style="left: 472.1164260324373px; top: 136.73527943898023px; background-color: #0000FF;"></div><div class="point" style="left: 391.2217001598732px; top: 456.0px; background-color: #0000FF;"></div><div class="point" style="left: 407.54686967571666px; top: 456.0px; background-color: #0000FF;"></div><div class="point" style="left: 404.5234922394524px; top: 447.0801995228847px; background-color: #0000FF;"></div><div class="point" style="left: 353.7292474802446px; top: 456.0px; background-color: #0000FF;"></div><div class="point" style="left: 370.366937517484px; top: 456.0px; background-color: #0000FF;"></div>
    </div>

    <h2>聚类统计信息</h2>
    <table border="1" style="border-collapse: collapse;">
        <tr><th>簇ID</th><th>目标数量</th><th>颜色</th></tr>
<tr><td>簇 0</td><td>2</td><td style="background-color: #FF0000; width: 50px;">&nbsp;</td></tr><tr><td>簇 1</td><td>3</td><td style="background-color: #00FF00; width: 50px;">&nbsp;</td></tr><tr><td>簇 2</td><td>15</td><td style="background-color: #0000FF; width: 50px;">&nbsp;</td></tr>
    </table>
</body>
</html>
