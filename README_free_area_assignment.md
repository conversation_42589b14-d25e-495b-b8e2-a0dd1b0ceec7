# 自由区域点分配算法 (Free Area Point Assignment Algorithm)

## 概述

本项目实现了一个基于YOLOv8实例分割结果的"自由区域点分配算法"，用于为蘑菇采摘任务中的每个目标mask的几何中心分配最优的自由区域点。

## 算法特点

- **输入**: 原始图像 + YOLOv8实例分割的轮廓JSON文件
- **输出**: 可视化结果图像 + 详细分配结果CSV文件 + 算法统计信息
- **处理对象**: 101个蘑菇目标mask
- **核心思想**: 通过占用图动态管理区域占用状态，为每个mask找到最优自由点

## 算法步骤详解

### Step 0: 可视化设置
- 从JSON文件加载mask轮廓数据
- 在原始图像上绘制轮廓（仅轮廓，无边界框）
- 创建结果可视化图像

### Step 1: 区域定义
- **占用区域**: mask覆盖的区域
- **自由区域**: mask外部的区域

### Step 2: 初始化占用图
- 创建与原始图像相同尺寸的占用图，初始值为0
- 占用图表示目标mask及其扩展区域的占用程度

### Step 3: 创建扩展区域
对每个目标mask：
- 计算原始mask的几何中心
- 将每个轮廓点从几何中心向外扩展，距离加倍
- 生成与原始mask形状相同但面积更大的扩展区域
- 在可视化图像上绘制扩展区域轮廓

### Step 4: 更新占用图
对每个mask：
- 原始mask区域内所有像素 +100
- 扩展区域与原始区域之间的环形区域内所有像素 +1

### Step 5: 处理所有Mask
- 重复Step 3-4处理所有mask
- 最终占用图中，原始mask区域值≥100（可能因其他mask扩展区域重叠而增加）

### Step 6: 寻找最优自由点
- 选择几何中心占用值最小的mask
- 在该mask的扩展区域内找到占用值最小的点（包括值为0的自由区域点）
- 在可视化图像上：
  - 绘制从几何中心到最优点的箭头
  - 标记序号（从1开始递增）

### Step 7: 更新占用图
处理当前mask后：
- 从原始mask区域减去100
- 从扩展环形区域减去1
- 为下一轮迭代创建更新的占用图

### Step 8: 迭代完成
- 重复Step 6-7直到处理完所有mask目标

## 实现结果

### 算法统计
- **总处理mask数量**: 101个
- **几何中心占用值**: 最小100，最大103，平均100.84
- **最优点占用值**: 最小1，最大3，平均1.07
- **中心到最优点距离**: 最小13.15像素，最大136.53像素，平均58.04像素

### Final Occupancy Map分析
- **图像尺寸**: 480×768像素（总计368,640像素）
- **占用值范围**: 0-206
- **平均占用值**: 53.75 ± 50.44
- **区域分布**:
  - 自由区域（值=0）: 4,989像素（1.4%）
  - 低占用区域（1-9）: 178,997像素（48.6%）
  - 中等占用区域（10-99）: 0像素（0.0%）
  - 高占用区域（≥100）: 184,654像素（50.1%）

### 输出文件
1. **`free_area_assignment_result.jpg`**: 完整算法结果可视化
   - 原始mask轮廓（彩色粗线）
   - 扩展区域轮廓（彩色细线）
   - 几何中心点（彩色实心圆）
   - 最优自由点（白色小圆）
   - 指向箭头（彩色箭头）
   - 序号标签（彩色数字）

2. **`free_area_assignment_results.csv`**: 详细分配结果
   - 包含每个mask的序号、索引、中心坐标、占用值、最优点坐标、距离等信息

3. **`final_occupancy_heatmap.jpg`**: Step 5完成后的占用图热力图（OpenCV版本）
   - 使用JET颜色映射显示占用值分布
   - 蓝色表示低占用值，红色表示高占用值
   - 包含最大值和最小值标注

4. **`final_occupancy_heatmap_matplotlib.png`**: 占用图热力图（Matplotlib版本）
   - 使用viridis颜色映射，更适合科学可视化
   - 包含颜色条和详细统计信息
   - 支持对数缩放以更好显示不同占用值

5. **`steps/`目录**: 算法步骤可视化
   - `step0_original_contours.jpg`: 原始轮廓和几何中心
   - `step1_extended_contours.jpg`: 扩展轮廓演示

## 核心函数

### `free_area_point_assignment(image_path, json_path, output_path=None)`
主要算法实现函数
- **参数**: 图像路径、JSON路径、输出路径（可选）
- **返回**: 结果图像、分配结果列表

### `calculate_geometric_center(contour_points)`
计算轮廓几何中心

### `extend_contour_from_center(contour_points, center)`
从几何中心向外扩展轮廓点

### `find_min_occupancy_point_in_region(occupancy_map, extended_mask, original_center)`
在扩展区域内找到占用值最小的点

### `print_algorithm_statistics(assignments)`
打印算法统计信息

### `save_assignment_results(assignments, output_file)`
保存分配结果到CSV文件

### `save_occupancy_heatmap(occupancy_map, output_path, title)`
使用Matplotlib将占用图保存为热力图
- 支持对数缩放和自定义颜色映射
- 包含详细的统计信息和颜色条

### `save_occupancy_heatmap_opencv(occupancy_map, output_path)`
使用OpenCV将占用图保存为热力图
- 不依赖matplotlib，使用JET颜色映射
- 在图像上直接标注最大值和最小值

### `analyze_occupancy_map(occupancy_map)`
分析占用图的统计信息
- 返回包含各种统计数据的字典
- 包括不同占用值范围的像素分布

### `print_occupancy_analysis(occupancy_map)`
打印占用图的详细分析结果

## 使用方法

```python
# 基本使用
result_img, assignments = free_area_point_assignment(
    "test_files/2_20210615122806_color.jpg",
    "test_files/2_20210615122806_color_contours.json",
    "output_result.jpg"
)

# 查看统计信息
print_algorithm_statistics(assignments)

# 保存详细结果
save_assignment_results(assignments, "results.csv")
```

## 算法优势

1. **动态占用管理**: 通过占用图实时跟踪区域占用状态
2. **形状保持扩展**: 扩展区域保持原始mask的形状特征
3. **全局最优选择**: 每次选择当前最优的mask进行处理
4. **完整可视化**: 提供详细的算法过程和结果可视化
5. **数据导出**: 支持CSV格式的详细结果导出

## 应用场景

- 机器人采摘路径规划
- 目标检测后处理
- 空间占用分析
- 避障路径规划
