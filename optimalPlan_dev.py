import json
import cv2
import numpy as np
from pathlib import Path
import math
'''
【250903】基于yolov8-1实例分割的mask，实现蘑菇采摘的最佳方案（采摘顺序+采摘方向）
'''

OBJECT_OCCUPY = 100
EXTENDED_OCCUPY = 10

def load_and_draw_masks(image_path, json_path, colors=None):
    """从JSON文件读取轮廓点并绘制到原图上"""
    # 读取原图
    img = cv2.imread(str(image_path))
    if img is None:
        raise ValueError(f"Cannot load image: {image_path}")
    
    # 读取轮廓数据
    with open(json_path, 'r') as f:
        contours_data = json.load(f)
    
    # 创建mask画布
    mask_overlay = img.copy()
    
    # 默认颜色调色板
    if colors is None:
        colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
            (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)
        ]
    
    for i, obj_data in enumerate(contours_data):
        contours = obj_data['contours']
        box = obj_data['box']
        
        # 选择颜色
        color = colors[i % len(colors)]
        
        # 绘制每个轮廓
        for contour in contours:
            if len(contour) > 0:
                # 转换为numpy数组
                contour_np = np.array(contour, dtype=np.int32).reshape(-1, 1, 2)
                
                # 填充mask
                cv2.fillPoly(mask_overlay, [contour_np], color)
                
                # 绘制轮廓边界
                cv2.polylines(img, [contour_np], True, color, 2)
        
        # 绘制边界框（可选）
        if len(box) >= 4:
            x1, y1, x2, y2 = map(int, box[:4])
            cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
    
    # 混合原图和mask
    result = cv2.addWeighted(img, 0.7, mask_overlay, 0.3, 0)
    
    return result, img, mask_overlay

def create_individual_masks(image_shape, json_path):
    """创建单独的mask数组"""
    with open(json_path, 'r') as f:
        contours_data = json.load(f)
    
    masks = []
    for obj_data in contours_data:
        # 创建单个mask
        mask = np.zeros(image_shape[:2], dtype=np.uint8)
        
        for contour in obj_data['contours']:
            if len(contour) > 0:
                contour_np = np.array(contour, dtype=np.int32).reshape(-1, 1, 2)
                cv2.fillPoly(mask, [contour_np], 255)
        
        masks.append(mask)
    
    return np.array(masks)

def calculate_geometric_center(contour_points):
    """计算轮廓的几何中心"""
    if not contour_points:
        return None

    contour_np = np.array(contour_points, dtype=np.float32)
    center_x = np.mean(contour_np[:, 0])
    center_y = np.mean(contour_np[:, 1])
    return (int(center_x), int(center_y))

def extend_contour_from_center(contour_points, center):
    """从几何中心向外扩展轮廓点，距离三倍"""
    extended_points = []
    center_x, center_y = center

    for point in contour_points:
        x, y = point
        # 计算从中心到当前点的向量
        dx = x - center_x
        dy = y - center_y

        # 扩展距离（三倍）
        extended_x = center_x + 3 * dx
        extended_y = center_y + 3 * dy

        extended_points.append([int(extended_x), int(extended_y)])

    return extended_points

def create_mask_from_contour(image_shape, contour_points):
    """从轮廓点创建mask"""
    mask = np.zeros(image_shape[:2], dtype=np.uint8)
    if contour_points:
        contour_np = np.array(contour_points, dtype=np.int32).reshape(-1, 1, 2)
        cv2.fillPoly(mask, [contour_np], 255)
    return mask

def find_min_occupancy_point_in_region(occupancy_map, extended_mask, original_center):
    """在扩展区域内找到占用值最小的点"""
    # 获取扩展区域内的所有点
    extended_coords = np.where(extended_mask == 255)

    if len(extended_coords[0]) == 0:
        return original_center

    min_occupancy = float('inf')
    best_point = original_center

    # 遍历扩展区域内的所有点
    for i in range(len(extended_coords[0])):
        y, x = extended_coords[0][i], extended_coords[1][i]
        occupancy_value = occupancy_map[y, x]

        if occupancy_value < min_occupancy:
            min_occupancy = occupancy_value
            best_point = (x, y)

    return best_point

def batch_process_masks(image_dir, mask_dir, output_dir):
    """批量处理mask文件
    # 使用示例
    batch_process_masks("images/", "runs/segment/masks/", "output_masked/")
    """
    image_dir = Path(image_dir)
    mask_dir = Path(mask_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    for json_file in mask_dir.glob("*_contours.json"):
        # 找到对应的图像文件
        img_stem = json_file.stem.replace("_contours", "")
        
        # 尝试不同的图像格式
        img_path = None
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            candidate = image_dir / f"{img_stem}{ext}"
            if candidate.exists():
                img_path = candidate
                break
        
        if img_path is None:
            print(f"Image not found for {json_file}")
            continue
        
        try:
            result, _, _ = load_and_draw_masks(img_path, json_file)
            output_path = output_dir / f"{img_stem}_masked.jpg"
            cv2.imwrite(str(output_path), result)
            print(f"Processed: {img_path} -> {output_path}")
        except Exception as e:
            print(f"Error processing {img_path}: {e}")

def free_area_point_assignment(image_path, json_path, output_path=None):
    """
    实现自由区域点分配算法

    Args:
        image_path: 原始图像路径
        json_path: 轮廓JSON文件路径
        output_path: 输出图像路径（可选）

    Returns:
        result_image: 带有算法结果的可视化图像
        assignments: 分配结果列表
    """
    # Step 0: 加载图像和轮廓数据
    img = cv2.imread(str(image_path))
    if img is None:
        raise ValueError(f"Cannot load image: {image_path}")

    with open(json_path, 'r') as f:
        contours_data = json.load(f)

    # 创建可视化图像
    result_img = img.copy()

    # Step 1 & 2: 初始化占用图
    occupancy_map = np.zeros(img.shape[:2], dtype=np.int32)

    # 准备mask数据
    mask_info = []
    colors = [
        (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
        (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0),
        (255, 192, 203), (0, 128, 128), (128, 128, 0), (128, 0, 0)
    ]

    for i, obj_data in enumerate(contours_data):
        contours = obj_data['contours']
        if not contours or not contours[0]:  # 跳过空轮廓
            continue

        # 合并所有轮廓点（如果有多个轮廓）
        all_points = []
        for contour in contours:
            all_points.extend(contour)

        if not all_points:
            continue
        
        # 如果轮廓的面积小于指定数量的像素，也跳过
        contour_area = cv2.contourArea(np.array(all_points, dtype=np.int32))
        if contour_area < 600:
            continue

        # 计算几何中心
        center = calculate_geometric_center(all_points)
        if center is None:
            continue

        # 创建原始mask
        original_mask = create_mask_from_contour(img.shape, all_points)

        # 扩展轮廓
        extended_points = extend_contour_from_center(all_points, center)
        extended_mask = create_mask_from_contour(img.shape, extended_points)

        mask_info.append({
            'index': i,
            'center': center,
            'original_points': all_points,
            'extended_points': extended_points,
            'original_mask': original_mask,
            'extended_mask': extended_mask,
            'color': colors[i % len(colors)]
        })

    # Step 3-5: 创建初始占用图
    for mask_data in mask_info:
        # 原始mask区域 +100
        occupancy_map[mask_data['original_mask'] == 255] += OBJECT_OCCUPY

        # 扩展区域（环形区域）+1
        annular_region = mask_data['extended_mask'] - mask_data['original_mask']
        occupancy_map[annular_region == 255] += EXTENDED_OCCUPY

    # 绘制原始轮廓
    for mask_data in mask_info:
        contour_np = np.array(mask_data['original_points'], dtype=np.int32).reshape(-1, 1, 2)
        cv2.polylines(result_img, [contour_np], True, mask_data['color'], 2)

        # 绘制扩展区域轮廓
        extended_contour_np = np.array(mask_data['extended_points'], dtype=np.int32).reshape(-1, 1, 2)
        cv2.polylines(result_img, [extended_contour_np], True, mask_data['color'], 1)

    # Step 5完成：保存final occupancy map热力图
    if output_path:
        # 生成热力图文件路径
        from pathlib import Path
        output_dir = Path(output_path).parent
        heatmap_path = output_dir / "final_occupancy_heatmap.jpg"
        heatmap_matplotlib_path = output_dir / "final_occupancy_heatmap_matplotlib.png"

        # 保存OpenCV版本的热力图
        save_occupancy_heatmap_opencv(occupancy_map, str(heatmap_path))

        # 尝试保存matplotlib版本的热力图（如果matplotlib可用）
        try:
            save_occupancy_heatmap(occupancy_map, str(heatmap_matplotlib_path),
                                 "Occupancy Map")
        except ImportError:
            print("matplotlib不可用，跳过matplotlib版本的热力图生成")

        # 打印占用图分析
        print_occupancy_analysis(occupancy_map)

    # Step 6-8: 迭代处理所有mask
    assignments = []
    processed_masks = set()
    sequence_number = 1

    while len(processed_masks) < len(mask_info):
        # Step 6: 选择几何中心占用值最小的mask
        min_center_occupancy = float('inf')
        selected_mask_idx = None

        for i, mask_data in enumerate(mask_info):
            if i in processed_masks:
                continue

            center_x, center_y = mask_data['center']
            center_occupancy = occupancy_map[center_y, center_x]

            if center_occupancy < min_center_occupancy:
                min_center_occupancy = center_occupancy
                selected_mask_idx = i

        if selected_mask_idx is None:
            break

        selected_mask = mask_info[selected_mask_idx]

        # 在扩展区域内找到占用值最小的点
        optimal_point = find_min_occupancy_point_in_region(
            occupancy_map, selected_mask['extended_mask'], selected_mask['center']
        )

        # 记录分配结果
        assignment = {
            'mask_index': selected_mask_idx,
            'center': selected_mask['center'],
            'optimal_point': optimal_point,
            'sequence': sequence_number,
            'center_occupancy': min_center_occupancy,
            'optimal_occupancy': occupancy_map[optimal_point[1], optimal_point[0]]
        }
        assignments.append(assignment)

        # 绘制箭头和标签
        cv2.arrowedLine(result_img, selected_mask['center'], optimal_point,
                       selected_mask['color'], 1, tipLength=0.2)

        # 绘制序号标签
        label_pos = (optimal_point[0] + 2, optimal_point[1] - 2)
        cv2.putText(result_img, str(sequence_number), label_pos,
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, selected_mask['color'], 2)

        # 绘制几何中心点
        cv2.circle(result_img, selected_mask['center'], 2, selected_mask['color'], -1)

        # 绘制最优点
        cv2.circle(result_img, optimal_point, 2, (255, 255, 255), -1)

        # Step 7: 更新占用图
        # 减去当前mask的贡献
        occupancy_map[selected_mask['original_mask'] == 255] -= OBJECT_OCCUPY
        annular_region = selected_mask['extended_mask'] - selected_mask['original_mask']
        occupancy_map[annular_region == 255] -= EXTENDED_OCCUPY

        processed_masks.add(selected_mask_idx)
        sequence_number += 1

    # 保存结果图像
    if output_path:
        cv2.imwrite(str(output_path), result_img)
        print(f"Result saved to: {output_path}")

    return result_img, assignments

def print_algorithm_statistics(assignments):
    """打印算法统计信息"""
    if not assignments:
        print("没有分配结果")
        return

    print(f"\n=== 自由区域点分配算法统计 ===")
    print(f"总处理mask数量: {len(assignments)}")

    # 统计占用值分布
    center_occupancies = [a['center_occupancy'] for a in assignments]
    optimal_occupancies = [a['optimal_occupancy'] for a in assignments]

    print(f"\n几何中心占用值统计:")
    print(f"  最小值: {min(center_occupancies)}")
    print(f"  最大值: {max(center_occupancies)}")
    print(f"  平均值: {sum(center_occupancies)/len(center_occupancies):.2f}")

    print(f"\n最优点占用值统计:")
    print(f"  最小值: {min(optimal_occupancies)}")
    print(f"  最大值: {max(optimal_occupancies)}")
    print(f"  平均值: {sum(optimal_occupancies)/len(optimal_occupancies):.2f}")

    # 计算距离统计
    distances = []
    for assignment in assignments:
        center = assignment['center']
        optimal = assignment['optimal_point']
        distance = math.sqrt((center[0] - optimal[0])**2 + (center[1] - optimal[1])**2)
        distances.append(distance)

    print(f"\n中心到最优点距离统计:")
    print(f"  最小距离: {min(distances):.2f} 像素")
    print(f"  最大距离: {max(distances):.2f} 像素")
    print(f"  平均距离: {sum(distances)/len(distances):.2f} 像素")

    # 显示前10个处理的mask
    print(f"\n前10个处理的mask详情:")
    for i, assignment in enumerate(assignments[:10]):
        distance = math.sqrt((assignment['center'][0] - assignment['optimal_point'][0])**2 +
                           (assignment['center'][1] - assignment['optimal_point'][1])**2)
        print(f"  {i+1:2d}. Mask {assignment['mask_index']:2d}: "
              f"中心占用{assignment['center_occupancy']:3d} -> "
              f"最优占用{assignment['optimal_occupancy']:3d}, "
              f"距离{distance:6.2f}px")

def save_assignment_results(assignments, output_file):
    """保存分配结果到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("序号,Mask索引,中心X,中心Y,中心占用值,最优点X,最优点Y,最优点占用值,距离\n")

        for assignment in assignments:
            center = assignment['center']
            optimal = assignment['optimal_point']
            distance = math.sqrt((center[0] - optimal[0])**2 + (center[1] - optimal[1])**2)

            f.write(f"{assignment['sequence']},"
                   f"{assignment['mask_index']},"
                   f"{center[0]},{center[1]},"
                   f"{assignment['center_occupancy']},"
                   f"{optimal[0]},{optimal[1]},"
                   f"{assignment['optimal_occupancy']},"
                   f"{distance:.2f}\n")

    print(f"分配结果已保存到: {output_file}")

def create_step_by_step_visualization(image_path, json_path, output_dir="test_files/steps"):
    """创建算法步骤的可视化图像"""
    from pathlib import Path

    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)

    # 加载数据
    img = cv2.imread(str(image_path))
    with open(json_path, 'r') as f:
        contours_data = json.load(f)

    # Step 0: 原始图像 + 轮廓
    step0_img = img.copy()
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]

    for i, obj_data in enumerate(contours_data[:10]):  # 只显示前10个用于演示
        contours = obj_data['contours']
        if not contours or not contours[0]:
            continue

        all_points = []
        for contour in contours:
            all_points.extend(contour)

        if not all_points:
            continue

        color = colors[i % len(colors)]
        contour_np = np.array(all_points, dtype=np.int32).reshape(-1, 1, 2)
        cv2.polylines(step0_img, [contour_np], True, color, 2)

        # 标记几何中心
        center = calculate_geometric_center(all_points)
        if center:
            cv2.circle(step0_img, center, 5, color, -1)
            cv2.putText(step0_img, str(i), (center[0]+10, center[1]-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    cv2.imwrite(str(output_dir / "step0_original_contours.jpg"), step0_img)

    # Step 1: 扩展轮廓
    step1_img = img.copy()
    for i, obj_data in enumerate(contours_data[:10]):
        contours = obj_data['contours']
        if not contours or not contours[0]:
            continue

        all_points = []
        for contour in contours:
            all_points.extend(contour)

        if not all_points:
            continue

        color = colors[i % len(colors)]
        center = calculate_geometric_center(all_points)

        # 原始轮廓
        contour_np = np.array(all_points, dtype=np.int32).reshape(-1, 1, 2)
        cv2.polylines(step1_img, [contour_np], True, color, 2)

        # 扩展轮廓
        extended_points = extend_contour_from_center(all_points, center)
        extended_contour_np = np.array(extended_points, dtype=np.int32).reshape(-1, 1, 2)
        cv2.polylines(step1_img, [extended_contour_np], True, color, 1)

        # 几何中心
        cv2.circle(step1_img, center, 5, color, -1)

    cv2.imwrite(str(output_dir / "step1_extended_contours.jpg"), step1_img)

    print(f"步骤可视化图像已保存到: {output_dir}")

def save_occupancy_heatmap(occupancy_map, output_path, title="Final Occupancy Map"):
    """
    将占用图保存为热力图可视化

    Args:
        occupancy_map: 占用图数组
        output_path: 输出文件路径
        title: 图像标题
    """
    import matplotlib.pyplot as plt
    import matplotlib.colors as colors

    # 创建图像
    plt.figure(figsize=(12, 8))

    # 创建自定义颜色映射
    # 0值用深蓝色，1-99用渐变色，100+用红色系
    try:
        cmap = plt.colormaps.get_cmap('viridis')  # matplotlib 3.7+
    except AttributeError:
        cmap = plt.cm.get_cmap('viridis')  # 旧版本兼容

    # 设置颜色范围，让不同占用值有明显区分
    max_val = np.max(occupancy_map)
    min_val = np.min(occupancy_map)

    # 使用对数缩放来更好地显示不同占用值
    norm = colors.LogNorm(vmin=max(1, min_val), vmax=max_val) if max_val > 1 else colors.Normalize(vmin=min_val, vmax=max_val)

    # 绘制热力图
    im = plt.imshow(occupancy_map, cmap=cmap, norm=norm, origin='upper')

    # 添加颜色条
    cbar = plt.colorbar(im, shrink=0.8)
    cbar.set_label('Occupancy Value', rotation=270, labelpad=20)

    # 设置标题和标签
    plt.title(f'{title}\nMax: {max_val}, Min: {min_val}', fontsize=14)
    plt.xlabel('X (pixels)')
    plt.ylabel('Y (pixels)')

    # 保存图像
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图像以释放内存

    print(f"占用图热力图已保存到: {output_path}")

def save_occupancy_heatmap_opencv(occupancy_map, output_path):
    """
    使用OpenCV将占用图保存为热力图（不依赖matplotlib）

    Args:
        occupancy_map: 占用图数组
        output_path: 输出文件路径
    """
    # 归一化占用图到0-255范围
    if np.max(occupancy_map) > 0:
        normalized = ((occupancy_map - np.min(occupancy_map)) /
                     (np.max(occupancy_map) - np.min(occupancy_map)) * 255).astype(np.uint8)
    else:
        normalized = np.zeros_like(occupancy_map, dtype=np.uint8)

    # 应用颜色映射
    heatmap = cv2.applyColorMap(normalized, cv2.COLORMAP_JET)

    # 添加文本信息
    max_val = np.max(occupancy_map)
    min_val = np.min(occupancy_map)

    # 在图像上添加统计信息
    cv2.putText(heatmap, f'Max: {max_val}', (10, 30),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.putText(heatmap, f'Min: {min_val}', (10, 60),
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

    # 保存图像
    cv2.imwrite(output_path, heatmap)
    print(f"占用图热力图(OpenCV版本)已保存到: {output_path}")

def analyze_occupancy_map(occupancy_map):
    """
    分析占用图的统计信息

    Args:
        occupancy_map: 占用图数组

    Returns:
        dict: 包含各种统计信息的字典
    """
    # 基本统计
    total_pixels = occupancy_map.size
    max_val = np.max(occupancy_map)
    min_val = np.min(occupancy_map)
    mean_val = np.mean(occupancy_map)
    std_val = np.std(occupancy_map)

    # 不同占用值范围的像素统计
    free_pixels = np.sum(occupancy_map == 0)  # 完全自由区域
    low_occupancy = np.sum((occupancy_map > 0) & (occupancy_map < 30))  # 低占用
    medium_occupancy = np.sum((occupancy_map >= 30) & (occupancy_map < 100))  # 中等占用
    high_occupancy = np.sum(occupancy_map >= 100)  # 高占用（原始mask区域）

    # 计算百分比
    free_percent = (free_pixels / total_pixels) * 100
    low_percent = (low_occupancy / total_pixels) * 100
    medium_percent = (medium_occupancy / total_pixels) * 100
    high_percent = (high_occupancy / total_pixels) * 100

    analysis = {
        'total_pixels': total_pixels,
        'max_value': max_val,
        'min_value': min_val,
        'mean_value': mean_val,
        'std_value': std_val,
        'free_pixels': free_pixels,
        'low_occupancy_pixels': low_occupancy,
        'medium_occupancy_pixels': medium_occupancy,
        'high_occupancy_pixels': high_occupancy,
        'free_percent': free_percent,
        'low_percent': low_percent,
        'medium_percent': medium_percent,
        'high_percent': high_percent
    }

    return analysis

def print_occupancy_analysis(occupancy_map):
    """打印占用图分析结果"""
    analysis = analyze_occupancy_map(occupancy_map)

    print(f"\n=== Final Occupancy Map 分析 ===")
    print(f"图像尺寸: {occupancy_map.shape}")
    print(f"总像素数: {analysis['total_pixels']:,}")
    print(f"占用值范围: {analysis['min_value']} - {analysis['max_value']}")
    print(f"平均占用值: {analysis['mean_value']:.2f} ± {analysis['std_value']:.2f}")

    print(f"\n占用区域分布:")
    print(f"  自由区域 (值=0):     {analysis['free_pixels']:8,} 像素 ({analysis['free_percent']:5.1f}%)")
    print(f"  低占用区域 (1-29):   {analysis['low_occupancy_pixels']:8,} 像素 ({analysis['low_percent']:5.1f}%)")
    print(f"  中等占用区域 (30-99): {analysis['medium_occupancy_pixels']:8,} 像素 ({analysis['medium_percent']:5.1f}%)")
    print(f"  高占用区域 (≥100):   {analysis['high_occupancy_pixels']:8,} 像素 ({analysis['high_percent']:5.1f}%)")

# 使用示例
if __name__ == "__main__":
    image_path = "test_files/2_20210615124113_color.jpg"
    json_path = "test_files/2_20210615124113_color_contours.json"
    output_path = "test_files/plan_result.jpg"

    print("开始执行自由区域点分配算法...")

    # 执行自由区域点分配算法
    result_img, assignments = free_area_point_assignment(image_path, json_path, output_path)

    # # 打印统计信息
    # print_algorithm_statistics(assignments)

    # # 保存详细结果到CSV文件
    # csv_output_path = "test_files/free_area_assignment_results.csv"
    # save_assignment_results(assignments, csv_output_path)

    # # 创建步骤可视化
    # print("\n创建算法步骤可视化...")
    # create_step_by_step_visualization(image_path, json_path)

    # # 显示结果
    # cv2.imshow('Free Area Point Assignment Result', result_img)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()

    # # 可选：显示原始方法的结果进行对比
    # print("\n显示原始轮廓绘制结果进行对比...")
    # result, original_with_contours, mask_overlay = load_and_draw_masks(image_path, json_path)
    # cv2.imshow('Original Method - Result', result)
    # cv2.imshow('Original Method - Contours Only', original_with_contours)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()